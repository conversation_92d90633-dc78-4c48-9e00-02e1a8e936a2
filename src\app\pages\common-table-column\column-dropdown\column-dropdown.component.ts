import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { MESSAGES } from '@constants/*';
import { AppToasterService } from '@core/services';
import { BaseComponent } from '@core/utils';
import { Message } from 'primeng/api';
import { Observable, forkJoin, map, takeUntil } from 'rxjs';
import { ColumnItem, FilterList } from '../models/common-table.column.model';
import { ColumnDropdownService } from './column-dropdown.service';

@Component({
  selector: 'app-column-dropdown',
  templateUrl: './column-dropdown.component.html',
  styleUrls: ['./column-dropdown.component.scss']
})
export class ColumnDropdownComponent extends BaseComponent implements OnInit, OnChanges {

  @Output() onClose: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Output() onSubmitCheck: EventEmitter<boolean> = new EventEmitter<boolean>();
  @Input() isModelVisible!: boolean;
  @Input() filterParams!: FilterList;
  @Input() specificationFilterParams!: FilterList;
  @Input() privateFilter!: ColumnItem[];
  @Input() columnsList!: ColumnItem[];
  @Input() isInventoryModule = false;
  ColumnFormGroup!: FormGroup;
  closeSidebar = false;
  searchInput!: any;
  selectedColumns: ColumnItem[] = [];
  defaultSelectedColumns: ColumnItem[] = [];
  specificationSelectedColumns: ColumnItem[] = [];
  isLoading = true;
  tableColumn!: FilterList | undefined;
  messages: Message[] | undefined;
  constructor(
    readonly columnDropDownService: ColumnDropdownService,
    private readonly toasterService: AppToasterService,
  ) {
    super();
  }

  ngOnInit(): void {
    this.selectedColumns = this.privateFilter
    this.messages = [{ severity: 'info', detail: 'Configuring the column order of universal fields on this screen will apply and save the layout for the gallery screen.' }];
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.isModelVisible?.currentValue) {
      this.sortSelectedColumnList();
    }
  }

  disableColumnStyle(column: ColumnItem): Object {
    if (column.disable) {
      return {
        'background': '#E5E4E2',
        'cursor': 'default',
        'pointer-events': 'none',
        'opacity': '0.5'
      }
    } else {
      return {}
    }
  }

  closePopup() {
    this.onClose.emit(this.closeSidebar);
  }

  drop(event: CdkDragDrop<any>) {
    if (this.columnsList[event?.currentIndex]?.disable) {
      return;
    }
    moveItemInArray(this.columnsList, event.previousIndex, event.currentIndex);
    this.sortSelectedColumnList()
  }

  sortSelectedColumnList() {
    const tempList: ColumnItem[] = []
    this.columnsList.forEach((column: any, i: number) => {
      if (this.selectedColumns.find(i => i.key === column.key)) {
        tempList.push(column)
      }
    })


    this.selectedColumns = tempList;
  }

  onCheckboxChecked(column: any, event: Array<string>) {
    if (event.includes(column)) {
      this.selectedColumns.push(column)
    }
    else {
      this.selectedColumns = this.selectedColumns
        .filter(item => item.id !== column.id)
    }
    this.sortSelectedColumnList();
  }

  get defaultFilterInfoParams(): FilterList {
    return {
      id: this.filterParams?.id,
      module: this.filterParams.module,
      skeyeUserId: this.filterParams.skeyeUserId,
      dealerId: this.filterParams.dealerId,
      isDefault: false,
      deleted: false,
      isPrivate: true,
      filterName: 'Column filter',
      filterType: 'COLUMN',
      data: JSON.stringify(this.defaultSelectedColumns),
      hideField: null
    };
  }
  get specificationFilterInfoParams(): FilterList {
    return {
      id: this.specificationFilterParams?.id,
      module: this.specificationFilterParams?.module,
      skeyeUserId: this.specificationFilterParams?.skeyeUserId,
      dealerId: this.specificationFilterParams?.dealerId,
      isDefault: false,
      deleted: false,
      isPrivate: true,
      filterName: 'Column filter',
      filterType: 'COLUMN',
      data: JSON.stringify(this.specificationSelectedColumns),
      hideField: null
    };
  }

  private updateFilter(): void {
    forkJoin([
      this.updateDefaultFilter(),
      this.updateSpecificationFilter()
    ]).subscribe({
      next: () => {
        this.isModelVisible = false;
        this.onClose.emit(this.isModelVisible);
        this.onSubmitCheck.emit(true);
      }
    });
  }

  private updateDefaultFilter(): Observable<void> {
    if (this.filterParams?.id) {
      return this.columnDropDownService.update<FilterList>(this.defaultFilterInfoParams).pipe(
        takeUntil(this.destroy$),
        map(() => void 0)
      );
    } else {
      return this.columnDropDownService.add<FilterList>(this.defaultFilterInfoParams).pipe(
        takeUntil(this.destroy$),
        map(() => void 0)
      );
    }
  }

  private updateSpecificationFilter(): Observable<void> {
    if (this.specificationFilterParams?.id) {
      return this.columnDropDownService.update<FilterList>(this.specificationFilterInfoParams).pipe(
        takeUntil(this.destroy$),
        map(() => void 0)
      );
    } else {
      return this.columnDropDownService.add<FilterList>(this.specificationFilterInfoParams).pipe(
        takeUntil(this.destroy$),
        map(() => void 0)
      );
    }
  }


  onSubmit(): void {
    if (!this.selectedColumns.length) {
      this.toasterService.error(MESSAGES.columnNotSelect);
      return;
    }
    if (this.filterParams?.id) {
      this.selectedColumns.forEach(column => {
        if (column.isSpecificationField) {
          this.specificationSelectedColumns.push(column);
        } else {
          this.defaultSelectedColumns.push(column);
        }
      });
      this.updateFilter();
    }
  }

  onCancel(): void {
    this.onClose.emit(this.closeSidebar);
  }


  trackByColumnKey(index: number, column: any): void {
    return column.key;
  }
}
